import io
import json
import os
import re
import sys
import traceback
from contextlib import redirect_stderr, redirect_stdout
from enum import Enum
from functools import reduce
from pathlib import Path
from tempfile import TemporaryDirectory
from typing import Annotated, Any, Dict, List, Literal, Optional, Tuple, Union

import matplotlib.pyplot as plt
# import mcp.types as mtypes  # Import mtypes for rich content display
import numpy as np  # type: ignore
import pandas as pd  # type: ignore
import polars as pl
import sklearn  # type: ignore
from langgraph_da_backend.utils.static import CODE_OUTPUT_DIR
from loguru import logger
from pydantic import AnyUrl, Field


def _format_floats_recursive(item: Any) -> Any:
    """
    Recursively traverses an item. If it's a float, formats it to 4 significant digits.
    If it's a list or dict, recursively applies formatting to its elements/values.
    Other types are returned as is.
    """
    if isinstance(item, float):
        return f"{item:.4g}"  # Format to 4 significant digits (e.g., 1.234, 0.01234, 12340, 1.234e+5)
    elif isinstance(item, list):
        return [_format_floats_recursive(elem) for elem in item]
    elif isinstance(item, dict):
        return {
            key: _format_floats_recursive(value) for key, value in item.items()
        }
    else:
        # Handles int, str, bool, None, etc.
        return item


def extract_python_code(llm_response: str) -> Optional[str]:
    """
    Extracts a Python code snippet from an LLM's response string.

    This function uses regex to find and extract the content from a Python code
    block, which might be enclosed in triple backticks. It handles cases with
    an optional "python" language specifier. If no code block is found, it
    assumes the entire string is code.

    Args:
        llm_response: The string response from the LLM, potentially
                      containing a Python code block.

    Returns:
        The extracted and cleaned Python code as a string, or None if the
        input is empty.
    """
    if not llm_response:
        return None

    # The pattern looks for a string enclosed in triple backticks,
    # with an optional "python" language identifier.
    # re.DOTALL allows '.' to match newline characters.
    code_block_pattern = r"```(?:python\n)?(.*?)```"
    match = re.search(code_block_pattern, llm_response, re.DOTALL)

    if match:
        # If a markdown block is found, extract the code inside
        return match.group(1).strip()
    else:
        # If no markdown block is found, assume the whole response is code
        return llm_response.strip()


def convert_to_json_compatible(data):
    """
    Recursively converts a dictionary's data types to be JSON-serializable.
    Handles common NumPy data types.
    """
    if isinstance(data, dict):
        return {
            key: convert_to_json_compatible(value)
            for key, value in data.items()
        }
    elif isinstance(data, list):
        return [convert_to_json_compatible(element) for element in data]
    elif isinstance(data, np.integer):
        return int(data)
    elif isinstance(data, np.floating):
        return float(data)
    elif isinstance(data, np.ndarray):
        return data.tolist()
    elif isinstance(data, np.bool_):
        return bool(data)
    else:
        return data


def run_sandboxed_code(
    code: str, target_df=pd.DataFrame
) -> Tuple[Dict[str, Any], Optional[pd.DataFrame], Optional[pd.DataFrame]]:
    logger.debug(f"Attempting to run sandboxed code ({type(code)}): \n{code}")

    exec_namespace = {
        "pd": pd,
        "pandas": pd,
        "pl": pl,
        "polars": pl,
        "np": np,
        "numpy": np,
        "plt": plt,
        "sklearn": sklearn,
        "df": target_df.copy(),
        "df_refined": None,
        "df_new": None,
        "final_result": None,
    }

    original_rows_num = target_df.shape[0]
    original_cols_set = set(target_df.columns)
    existing_objects = set(os.listdir(CODE_OUTPUT_DIR))

    stdout_capture = io.StringIO()
    stderr_capture = io.StringIO()

    execution_summary_message = ""

    with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
        # Execute the code
        # ! we use only one namespace (global) as the usage of local
        # ! could lead to scope issues when there are list comprehensions (lambda, generator, etc.)
        try:
            exec(code, exec_namespace)
        except Exception as e:
            logger.error(f"Error executing custom code: {e}")
            tb_str = traceback.format_exc()
            return (
                {
                    "status": "error",
                    "message": f"Error executing custom code: {str(e)}",
                    "stdout": stdout_capture.getvalue(),
                    "stderr": stderr_capture.getvalue(),
                    "traceback": tb_str,
                },
                None,
                None,
            )

    plt.close("all")
    # Retrieve results from the execution context
    df_refined = exec_namespace.get("df_refined")
    df_new = exec_namespace.get("df_new")
    final_result = exec_namespace.get("final_result")

    try:
        if isinstance(final_result, str):
            final_result_dict = _format_floats_recursive(
                json.loads(final_result)
            )
        else:
            final_result_dict = _format_floats_recursive(final_result)
    except Exception:
        pass
    finally:
        final_result = json.dumps(
            convert_to_json_compatible(final_result_dict), ensure_ascii=False
        )

    stdout_str = stdout_capture.getvalue()
    stderr_str = stderr_capture.getvalue()

    execution_summary_message = "Code executed successfully."
    if stdout_str:
        execution_summary_message += f"\n\nStdout:\n{stdout_str}"
    if stderr_str:
        execution_summary_message += f"\n\nStderr:\n{stderr_str}"

    if isinstance(df_refined, pd.DataFrame):
        result_cols_set = set(df_refined.columns)
        added_col_names = list(result_cols_set - original_cols_set)
        if df_refined.shape[0] == original_rows_num:
            execution_summary_message += f"\n\nNew columns added to original dataframe: {added_col_names}; no rows were dropped."
        else:
            execution_summary_message += f"\n\nNew columns added to original dataframe: {added_col_names}; {original_rows_num - df_refined.shape[0]} rows were dropped."

    if isinstance(df_new, pd.DataFrame):
        execution_summary_message += f"\n\nNew dataframe created: {df_new.shape[0]} × {len(df_new.columns)}, with columns: {df_new.columns.tolist()}"
        logger.debug(f"New dataframe: {df_new.head()}")

    full_message = execution_summary_message

    # Identify newly created figures
    current_objects = (
        set(os.listdir(CODE_OUTPUT_DIR))
        if os.path.exists(CODE_OUTPUT_DIR)
        else set()
    )
    new_objects = list(current_objects - existing_objects)

    rslt = {
        "status": "success",
        "message": full_message,
        "execution_result": final_result,
        "new_objects": new_objects,
    }

    return rslt, df_refined, df_new


def merge_lazyframes(
    main_column: str,
    lazyframes: list[pl.LazyFrame],
    column_mapping: dict[str, list[str]],
) -> pl.LazyFrame:
    """
    Merges a list of polars.LazyFrame objects into a new LazyFrame using a
    "join-insert" strategy.

    Args:
        main_column: The name of the main column to join on.
        lazyframes: A list of polars.LazyFrame objects to merge.
        column_mapping: A dictionary where keys are the new column names and
                        values are lists of original column names.

    Returns:
        A new merged polars.LazyFrame.
    """
    # Create a base LazyFrame with all unique values of the main column
    base_lf = pl.concat(
        [lf.select(pl.col(main_column)) for lf in lazyframes]
    ).unique()

    # Create a list of LazyFrames to be joined
    lfs_to_join = [base_lf]

    # Iterate through the column mapping to prepare each column for the join
    for new_col, original_cols in column_mapping.items():
        for original_col in original_cols:
            for lf in lazyframes:
                if original_col in lf.columns:
                    # Select the main column and the original column,
                    # then rename the original column to the new column name
                    lfs_to_join.append(
                        lf.select(
                            pl.col(main_column),
                            pl.col(original_col).alias(new_col),
                        )
                    )
                    break  # Move to the next new column once the original is found

    # Sequentially join all prepared LazyFrames
    merged_lf = reduce(
        lambda left, right: left.join(right, on=main_column, how="left"),
        lfs_to_join,
    )

    return merged_lf
