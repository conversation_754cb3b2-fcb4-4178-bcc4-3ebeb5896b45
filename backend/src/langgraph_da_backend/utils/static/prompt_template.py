DA_SANITY_CHECK_TEMPLATE = """
Check whether the user's query indicates a data analysis task or can be solved by data analysis.

User query:
{query}

Output instructions/format:
{format_instructions}
""".strip()

DA_REWRITE_TEMPLATE = """
Rewrite the user's query into a formal data analysis task. 

The relevant data schema is as follows:
{data_schema}

The original user query is:
'{query}'

The rewritten query should clarify the following aspects:
- What kind of this analysis should be performed? 
- What specific metrics or key indicators should be analyzed? Does these metrics exist in the original data or need to be calculated?
- What models or algorithms could be applied to provide insights? 

Output instructions/format:
{format_instructions}
"""

ABSTRACT_PLAN_TEMPLATE = """
Decompose the user's request into actionable steps. The steps would be executed in a sequential order: `data preparation`, `data analysis`, and optional `visualization`.

The original user request is:
'{query}'

A descriptive analysis has been performed on the mentioned data, as follows:
{data_description}

The decomposed steps should stick to the following guidelines:
1. Provide only what-to-do instructions. DO NOT include any explanation or ambiguous conditional statements like examples. If a step can be implemented in multiple ways, choose the most informative, most fine-grained one. Give clear, deterministic instructions.
2. All transformations should be finalized within the data preparation step. The data analysis step only performs computation on the prepared dataset.
3. Each of the data operation should specify the columns to work on.
""".strip()

ABSTRACT_PLAN_PARSE_TEMPLATE = """
{query}

{format_instructions}
""".strip()

DE_PIPELINE_TEMPLATE = """
Determine the operation details for preparing the data for downstream analysis.

The data schema (columns and data types) are as follows:
{data_schema}

The hints for curating the dataset: 
{curation_hint}

Constraints:
1. DO NOT refer to non-existent columns. Predefined tools only operate on existed columns.
2. Give clear what-to-do instructions only. DO NOT include any explanation or ambiguous conditional statements like examples.

Output instructions/format:  
{format_instructions}
""".strip()

DE_CODE_CURATION_TEMPLATE = """
Write a data processing python script to be executed in a sandbox environment, which is equivalent to pass the code to the `exec()` function. 

The data schema (columns and data types) are as follows:
{data_schema}

The hints for processing the dataset: 
{curation_hint}

Constraints:
1. Provide only the raw code that can be directly executed. DO NOT include any non-codeblock descriptions or explanations.
2. Except for built-in Python modules, only the following modules are allowed: `numpy`(`np`), `pandas`(`pd`), `plotly`, `polars`(`pl`), `scipy`, `sklearn`.
3. The data object which you need to operate on is provided as an in-memory python variable named `df`, whose type is `pandas.DataFrame`. This is the only available variable at the start.
4. Assign the processed data object into two new variables, both of which should be `pandas.DataFrame` type. 
    - If the processed data contains a subset of the original data columns, e.g., row-wise filtering, column-wise transformation or new column addition, assign the result data object to `df_refined` variable. 
    - If the processed data is an aggregation or other forms of tranformation and does not contain any original data columns, assign the result data object to `df_new` variable.
""".strip()
